// Payment routes and controllers for the university portal
import express from 'express';
import { authenticate, authorize } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';
import { PaymentServiceManager } from '../services/payment-service.js';
import { PaymentTransaction, PaymentMethod, Fee } from '../models/Payment.js';
import { logger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';

const router = express.Router();

// Payment configuration
const paymentConfig = {
  mpesa: {
    consumerKey: process.env.MPESA_CONSUMER_KEY!,
    consumerSecret: process.env.MPESA_CONSUMER_SECRET!,
    businessShortCode: process.env.MPESA_BUSINESS_SHORT_CODE!,
    passkey: process.env.MPESA_PASSKEY!,
    environment: process.env.MPESA_ENVIRONMENT as 'sandbox' | 'production',
    callbackUrl: process.env.MPESA_CALLBACK_URL!
  },
  jambopay: {
    apiKey: process.env.JAMBOPAY_API_KEY!,
    merchantId: process.env.JAMBOPAY_MERCHANT_ID!,
    environment: process.env.JAMBOPAY_ENVIRONMENT as 'sandbox' | 'production',
    callbackUrl: process.env.JAMBOPAY_CALLBACK_URL!
  },
  imBank: {
    clientId: process.env.IM_BANK_CLIENT_ID!,
    clientSecret: process.env.IM_BANK_CLIENT_SECRET!,
    baseUrl: process.env.IM_BANK_BASE_URL!,
    environment: process.env.IM_BANK_ENVIRONMENT as 'sandbox' | 'production'
  },
  kcbBuni: {
    apiKey: process.env.KCB_BUNI_API_KEY!,
    merchantId: process.env.KCB_BUNI_MERCHANT_ID!,
    baseUrl: process.env.KCB_BUNI_BASE_URL!,
    environment: process.env.KCB_BUNI_ENVIRONMENT as 'sandbox' | 'production'
  },
  pesalink: {
    apiKey: process.env.PESALINK_API_KEY!,
    merchantId: process.env.PESALINK_MERCHANT_ID!,
    baseUrl: process.env.PESALINK_BASE_URL!,
    environment: process.env.PESALINK_ENVIRONMENT as 'sandbox' | 'production'
  }
};

const paymentService = new PaymentServiceManager(paymentConfig);

// Get available payment methods
router.get('/methods', authenticate, async (req, res) => {
  try {
    const paymentMethods = await PaymentMethod.find({ isActive: true })
      .select('name code provider fees limits')
      .lean();

    res.json({
      success: true,
      data: paymentMethods
    });
  } catch (error) {
    logger.error('Failed to fetch payment methods', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment methods',
      error: error.message
    });
  }
});

// Get student's pending fees
router.get('/fees/pending', authenticate, async (req, res) => {
  try {
    const studentId = req.user.id;
    
    const pendingFees = await Fee.find({
      student: studentId,
      isPaid: false,
      isActive: true
    })
    .populate('course', 'code title')
    .sort({ dueDate: 1 })
    .lean();

    res.json({
      success: true,
      data: pendingFees
    });
  } catch (error) {
    logger.error('Failed to fetch pending fees', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending fees',
      error: error.message
    });
  }
});

// Initiate payment
router.post('/initiate', authenticate, validateRequest({
  body: {
    feeId: { type: 'string', required: true },
    paymentMethod: { type: 'string', required: true },
    phoneNumber: { type: 'string', required: false },
    accountNumber: { type: 'string', required: false },
    bankCode: { type: 'string', required: false }
  }
}), async (req, res) => {
  try {
    const { feeId, paymentMethod, phoneNumber, accountNumber, bankCode } = req.body;
    const studentId = req.user.id;

    // Validate fee exists and belongs to student
    const fee = await Fee.findOne({
      _id: feeId,
      student: studentId,
      isPaid: false,
      isActive: true
    });

    if (!fee) {
      return res.status(404).json({
        success: false,
        message: 'Fee not found or already paid'
      });
    }

    // Validate payment method
    const paymentMethodDoc = await PaymentMethod.findOne({ 
      code: paymentMethod, 
      isActive: true 
    });

    if (!paymentMethodDoc) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment method'
      });
    }

    // Validate payment amount
    if (fee.amount < paymentMethodDoc.limits.min || fee.amount > paymentMethodDoc.limits.max) {
      return res.status(400).json({
        success: false,
        message: `Payment amount must be between KES ${paymentMethodDoc.limits.min} and KES ${paymentMethodDoc.limits.max}`
      });
    }

    // Prepare payment request
    const paymentRequest = {
      studentId,
      feeId,
      amount: fee.totalAmount,
      phoneNumber,
      accountNumber,
      bankCode,
      description: `${fee.feeType} fee for ${fee.semester} ${fee.academicYear}`
    };

    // Initiate payment
    const paymentResponse = await paymentService.initiatePayment(paymentMethod, paymentRequest);

    if (paymentResponse.success) {
      // Send notification to student
      await publishRealtimeUpdate('payment_initiated', {
        notification: {
          userId: studentId,
          title: 'Payment Initiated',
          message: `Payment of KES ${fee.totalAmount} has been initiated via ${paymentMethodDoc.name}. Please complete the payment.`,
          type: 'info'
        },
        sentBy: 'system'
      });

      res.json({
        success: true,
        data: {
          referenceNumber: paymentResponse.referenceNumber,
          amount: fee.totalAmount,
          paymentMethod: paymentMethodDoc.name,
          message: paymentResponse.message
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: paymentResponse.message,
        error: paymentResponse.error
      });
    }

  } catch (error) {
    logger.error('Payment initiation failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Payment initiation failed',
      error: error.message
    });
  }
});

// Get payment transaction status
router.get('/transaction/:referenceNumber', authenticate, async (req, res) => {
  try {
    const { referenceNumber } = req.params;
    const studentId = req.user.id;

    const transaction = await PaymentTransaction.findOne({
      referenceNumber,
      student: studentId
    })
    .populate('fee', 'feeType amount semester academicYear')
    .populate('paymentMethod', 'name code provider')
    .lean();

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    logger.error('Failed to fetch transaction status', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction status',
      error: error.message
    });
  }
});

// Get student's payment history
router.get('/history', authenticate, async (req, res) => {
  try {
    const studentId = req.user.id;
    const { page = 1, limit = 20, status, paymentMethod } = req.query;

    const query: any = { student: studentId };
    
    if (status) {
      query.status = status;
    }
    
    if (paymentMethod) {
      const paymentMethodDoc = await PaymentMethod.findOne({ code: paymentMethod });
      if (paymentMethodDoc) {
        query.paymentMethod = paymentMethodDoc._id;
      }
    }

    const transactions = await PaymentTransaction.find(query)
      .populate('fee', 'feeType amount semester academicYear')
      .populate('paymentMethod', 'name code provider')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    const total = await PaymentTransaction.countDocuments(query);

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Failed to fetch payment history', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment history',
      error: error.message
    });
  }
});

// Retry failed payment
router.post('/retry/:transactionId', authenticate, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const studentId = req.user.id;

    const transaction = await PaymentTransaction.findOne({
      _id: transactionId,
      student: studentId,
      status: 'failed'
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found or cannot be retried'
      });
    }

    if (!transaction.canRetry()) {
      return res.status(400).json({
        success: false,
        message: 'Maximum retry attempts exceeded'
      });
    }

    // Increment retry count
    await transaction.incrementRetry();

    // Get payment method
    const paymentMethod = await PaymentMethod.findById(transaction.paymentMethod);
    
    // Prepare retry request
    const paymentRequest = {
      studentId: transaction.student.toString(),
      feeId: transaction.fee.toString(),
      amount: transaction.amount,
      phoneNumber: transaction.metadata.phoneNumber,
      accountNumber: transaction.metadata.accountNumber,
      bankCode: transaction.metadata.bankCode,
      description: `Retry payment for ${transaction.referenceNumber}`
    };

    // Retry payment
    const paymentResponse = await paymentService.initiatePayment(paymentMethod.code, paymentRequest);

    if (paymentResponse.success) {
      // Update transaction with new provider details
      transaction.providerTransactionId = paymentResponse.transactionId;
      transaction.status = 'initiated';
      transaction.metadata.providerResponse = paymentResponse.providerResponse;
      await transaction.save();

      res.json({
        success: true,
        message: 'Payment retry initiated successfully',
        data: {
          referenceNumber: transaction.referenceNumber,
          amount: transaction.amount
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: paymentResponse.message,
        error: paymentResponse.error
      });
    }

  } catch (error) {
    logger.error('Payment retry failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Payment retry failed',
      error: error.message
    });
  }
});

// Webhook endpoints for payment providers
router.post('/webhook/mpesa', async (req, res) => {
  try {
    const payload = req.body;
    const signature = req.headers['x-mpesa-signature'] as string;

    await paymentService.handleWebhook('mpesa', payload, signature);

    res.json({ success: true, message: 'Webhook processed successfully' });
  } catch (error) {
    logger.error('M-Pesa webhook processing failed', { error: error.message });
    res.status(400).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

router.post('/webhook/jambopay', async (req, res) => {
  try {
    const payload = req.body;
    const signature = req.headers['x-jambopay-signature'] as string;

    await paymentService.handleWebhook('jambopay', payload, signature);

    res.json({ success: true, message: 'Webhook processed successfully' });
  } catch (error) {
    logger.error('JamboPay webhook processing failed', { error: error.message });
    res.status(400).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

router.post('/webhook/im-bank', async (req, res) => {
  try {
    const payload = req.body;
    const signature = req.headers['x-im-bank-signature'] as string;

    await paymentService.handleWebhook('im_bank', payload, signature);

    res.json({ success: true, message: 'Webhook processed successfully' });
  } catch (error) {
    logger.error('I&M Bank webhook processing failed', { error: error.message });
    res.status(400).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

// Admin routes for payment management
router.get('/admin/transactions', authenticate, authorize(['admin', 'finance']), async (req, res) => {
  try {
    const { page = 1, limit = 20, status, paymentMethod, studentId, startDate, endDate } = req.query;

    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (studentId) {
      query.student = studentId;
    }
    
    if (paymentMethod) {
      const paymentMethodDoc = await PaymentMethod.findOne({ code: paymentMethod });
      if (paymentMethodDoc) {
        query.paymentMethod = paymentMethodDoc._id;
      }
    }
    
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate as string),
        $lte: new Date(endDate as string)
      };
    }

    const transactions = await PaymentTransaction.find(query)
      .populate('student', 'name email studentId')
      .populate('fee', 'feeType amount semester academicYear')
      .populate('paymentMethod', 'name code provider')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    const total = await PaymentTransaction.countDocuments(query);

    // Calculate summary statistics
    const summary = await PaymentTransaction.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        transactions,
        summary,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Failed to fetch admin transactions', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transactions',
      error: error.message
    });
  }
});

// Payment reconciliation endpoint
router.post('/admin/reconcile', authenticate, authorize(['admin', 'finance']), async (req, res) => {
  try {
    const { transactionId } = req.body;

    const transaction = await PaymentTransaction.findById(transactionId);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Implement reconciliation logic here
    // This would typically involve calling the payment provider's API
    // to verify the transaction status and amount

    res.json({
      success: true,
      message: 'Reconciliation completed successfully'
    });
  } catch (error) {
    logger.error('Payment reconciliation failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Payment reconciliation failed',
      error: error.message
    });
  }
});

export default router;
