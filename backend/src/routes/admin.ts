import express from 'express';
import { body, param, query, validationResult } from 'express-validator';

import { User, Course, Grade, Assignment, Attendance, Department, Notification } from '../models/index.js';
import { authenticate, requireRole, hasPermission } from '../middleware/auth.js';
import { redisService } from '../services/redis.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';

const router = express.Router();

// All admin routes require admin role
router.use(authenticate, requireRole('admin'));

// GET /api/admin/dashboard - Get admin dashboard statistics
router.get('/dashboard', async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfYear = new Date(now.getFullYear(), 0, 1);

    // Get comprehensive statistics
    const stats = {
      users: {
        total: await User.countDocuments({ isActive: true }),
        students: await User.countDocuments({ roles: 'student', isActive: true }),
        lecturers: await User.countDocuments({ roles: 'lecturer', isActive: true }),
        admins: await User.countDocuments({ roles: 'admin', isActive: true }),
        newThisMonth: await User.countDocuments({
          isActive: true,
          createdAt: { $gte: startOfMonth }
        })
      },
      courses: {
        total: await Course.countDocuments({ isActive: true }),
        active: await Course.countDocuments({ isActive: true }),
        newThisMonth: await Course.countDocuments({
          isActive: true,
          createdAt: { $gte: startOfMonth }
        })
      },
      assignments: {
        total: await Assignment.countDocuments(),
        graded: await Assignment.countDocuments({
          'submissions.gradedAt': { $exists: true }
        }),
        pending: await Assignment.countDocuments({
          'submissions.gradedAt': { $exists: false }
        })
      },
      attendance: {
        totalSessions: await Attendance.countDocuments(),
        present: await Attendance.countDocuments({ status: 'present' }),
        absent: await Attendance.countDocuments({ status: 'absent' }),
        late: await Attendance.countDocuments({ status: 'late' })
      },
      departments: {
        total: await Department.countDocuments({ isActive: true }),
        totalBudget: await Department.aggregate([
          { $match: { isActive: true } },
          { $group: { _id: null, total: { $sum: '$budget' } } }
        ])
      },
      notifications: {
        total: await Notification.countDocuments(),
        unread: await Notification.countDocuments({ read: false }),
        sentThisMonth: await Notification.countDocuments({
          createdAt: { $gte: startOfMonth }
        })
      }
    };

    // Get recent activities
    const recentActivities = await Promise.all([
      User.find({ isActive: true })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('name email roles createdAt'),
      Course.find({ isActive: true })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('title code lecturer createdAt')
        .populate('lecturer', 'name'),
      Grade.find()
        .sort({ gradedAt: -1 })
        .limit(5)
        .select('score grade gradedAt')
        .populate('student', 'name')
        .populate('course', 'code')
    ]);

    res.json({
      success: true,
      data: {
        stats,
        recentActivities: {
          users: recentActivities[0],
          courses: recentActivities[1],
          grades: recentActivities[2]
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching admin dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data',
      code: 'FETCH_DASHBOARD_ERROR'
    });
  }
});

// GET /api/admin/users - Get all users with admin-specific data
router.get('/users', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      role, 
      department, 
      search, 
      sortBy = 'createdAt', 
      sortOrder = 'desc' 
    } = req.query;
    
    // Build filter query
    const filter = { isActive: true };
    if (role) filter.roles = role;
    if (department) filter.department = department;
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { studentId: { $regex: search, $options: 'i' } },
        { staffId: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Execute query with pagination
    const users = await User.find(filter)
      .select('-password -refreshTokens')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(filter);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching admin users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      code: 'FETCH_USERS_ERROR'
    });
  }
});

// GET /api/admin/courses - Get all courses with admin-specific data
router.get('/courses', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      department, 
      lecturer, 
      semester,
      search,
      sortBy = 'createdAt', 
      sortOrder = 'desc' 
    } = req.query;
    
    // Build filter query
    const filter = { isActive: true };
    if (department) filter.department = department;
    if (lecturer) filter.lecturer = lecturer;
    if (semester) filter.semester = semester;
    if (search) {
      filter.$or = [
        { code: { $regex: search, $options: 'i' } },
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Execute query with pagination and population
    const courses = await Course.find(filter)
      .populate('lecturer', 'name email staffId')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Course.countDocuments(filter);

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching admin courses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch courses',
      code: 'FETCH_COURSES_ERROR'
    });
  }
});

// GET /api/admin/departments - Get all departments with admin-specific data
router.get('/departments', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search,
      sortBy = 'name', 
      sortOrder = 'asc' 
    } = req.query;
    
    // Build filter query
    const filter = { isActive: true };
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Execute query with pagination and population
    const departments = await Department.find(filter)
      .populate('head', 'name email staffId')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Department.countDocuments(filter);

    res.json({
      success: true,
      data: {
        departments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching admin departments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch departments',
      code: 'FETCH_DEPARTMENTS_ERROR'
    });
  }
});

// GET /api/admin/finance - Get financial overview
router.get('/finance', async (req, res) => {
  try {
    const { period = 'current' } = req.query;
    
    // Calculate date range
    const now = new Date();
    let startDate, endDate;
    
    switch (period) {
      case 'current':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'last':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), 11, 31);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }

    // Get financial data
    const finance = {
      departments: await Department.find({ isActive: true })
        .select('name budget')
        .sort({ budget: -1 }),
      totalBudget: await Department.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: null, total: { $sum: '$budget' } } }
      ]),
      // Mock financial data - in real implementation, this would come from financial records
      revenue: {
        tuition: 5000000,
        grants: 2000000,
        donations: 500000,
        other: 300000
      },
      expenses: {
        salaries: 4000000,
        facilities: 800000,
        equipment: 500000,
        other: 700000
      }
    };

    res.json({
      success: true,
      data: { finance }
    });
  } catch (error) {
    logger.error('Error fetching finance data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch finance data',
      code: 'FETCH_FINANCE_ERROR'
    });
  }
});

// GET /api/admin/reports - Get various reports
router.get('/reports', async (req, res) => {
  try {
    const { type = 'overview' } = req.query;
    
    let reports = {};

    switch (type) {
      case 'overview':
        reports = {
          userGrowth: await User.aggregate([
            {
              $group: {
                _id: {
                  year: { $year: '$createdAt' },
                  month: { $month: '$createdAt' }
                },
                count: { $sum: 1 }
              }
            },
            { $sort: { '_id.year': 1, '_id.month': 1 } }
          ]),
          courseEnrollment: await Course.aggregate([
            {
              $group: {
                _id: '$department',
                totalCourses: { $sum: 1 },
                totalEnrolled: { $sum: '$enrolled' }
              }
            }
          ]),
          gradeDistribution: await Grade.aggregate([
            {
              $group: {
                _id: '$grade',
                count: { $sum: 1 }
              }
            }
          ])
        };
        break;

      case 'attendance':
        reports = {
          overallAttendance: await Attendance.aggregate([
            {
              $group: {
                _id: '$status',
                count: { $sum: 1 }
              }
            }
          ]),
          attendanceByCourse: await Attendance.aggregate([
            {
              $lookup: {
                from: 'sessions',
                localField: 'session',
                foreignField: '_id',
                as: 'sessionData'
              }
            },
            {
              $unwind: '$sessionData'
            },
            {
              $lookup: {
                from: 'courses',
                localField: 'sessionData.course',
                foreignField: '_id',
                as: 'courseData'
              }
            },
            {
              $unwind: '$courseData'
            },
            {
              $group: {
                _id: {
                  course: '$courseData.code',
                  status: '$status'
                },
                count: { $sum: 1 }
              }
            }
          ])
        };
        break;

      case 'academic':
        reports = {
          gradeStatistics: await Grade.aggregate([
            {
              $group: {
                _id: '$course',
                averageScore: { $avg: '$score' },
                maxScore: { $max: '$score' },
                minScore: { $min: '$score' },
                count: { $sum: 1 }
              }
            },
            {
              $lookup: {
                from: 'courses',
                localField: '_id',
                foreignField: '_id',
                as: 'courseData'
              }
            },
            {
              $unwind: '$courseData'
            },
            {
              $project: {
                courseCode: '$courseData.code',
                courseTitle: '$courseData.title',
                averageScore: 1,
                maxScore: 1,
                minScore: 1,
                count: 1
              }
            }
          ])
        };
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid report type',
          code: 'INVALID_REPORT_TYPE'
        });
    }

    res.json({
      success: true,
      data: { reports }
    });
  } catch (error) {
    logger.error('Error fetching reports:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reports',
      code: 'FETCH_REPORTS_ERROR'
    });
  }
});

// POST /api/admin/broadcast - Send system-wide broadcast
router.post('/broadcast', async (req, res) => {
  try {
    const { title, message, type = 'info', targetRoles, targetDepartments } = req.body;

    if (!title || !message) {
      return res.status(400).json({
        success: false,
        message: 'Title and message are required',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // Create notification for all users or specific targets
    let targetUsers = [];
    
    if (targetRoles && Array.isArray(targetRoles)) {
      const users = await User.find({
        roles: { $in: targetRoles },
        isActive: true
      }).select('_id');
      targetUsers = users.map(u => u._id);
    } else if (targetDepartments && Array.isArray(targetDepartments)) {
      const users = await User.find({
        department: { $in: targetDepartments },
        isActive: true
      }).select('_id');
      targetUsers = users.map(u => u._id);
    } else {
      // Send to all users
      const users = await User.find({ isActive: true }).select('_id');
      targetUsers = users.map(u => u._id);
    }

    // Create notifications
    const notifications = targetUsers.map(userId => ({
      user: userId,
      title,
      message,
      type,
      metadata: {
        sentBy: req.user._id,
        sentAt: new Date(),
        broadcast: true
      }
    }));

    const savedNotifications = await Notification.insertMany(notifications);

    // Publish real-time updates
    for (const notification of savedNotifications) {
      await publishRealtimeUpdate('notification_created', {
        notification: {
          id: notification._id,
          userId: notification.user,
          title: notification.title,
          type: notification.type
        },
        sentBy: req.user._id
      });
    }

    // Log broadcast
    auditLogger('system_broadcast', 'notification', req.user._id, {
      title,
      type,
      targetCount: savedNotifications.length,
      targetRoles,
      targetDepartments
    });

    res.status(201).json({
      success: true,
      message: `Broadcast sent to ${savedNotifications.length} users`,
      data: { count: savedNotifications.length }
    });
  } catch (error) {
    logger.error('Error sending broadcast:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send broadcast',
      code: 'SEND_BROADCAST_ERROR'
    });
  }
});

// GET /api/admin/system-health - Get system health status
router.get('/system-health', async (req, res) => {
  try {
    const health = {
      database: {
        status: 'healthy',
        connections: 0, // This would be actual connection count
        responseTime: '< 10ms'
      },
      redis: {
        status: 'healthy',
        memory: '2.1GB',
        connections: 0
      },
      api: {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      services: {
        email: 'healthy',
        fileUpload: 'healthy',
        realtime: 'healthy'
      }
    };

    res.json({
      success: true,
      data: { health }
    });
  } catch (error) {
    logger.error('Error fetching system health:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system health',
      code: 'FETCH_SYSTEM_HEALTH_ERROR'
    });
  }
});

export default router;
