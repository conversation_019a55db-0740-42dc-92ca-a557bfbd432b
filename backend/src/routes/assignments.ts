import express from 'express';
import { body, param, query, validationResult } from 'express-validator';

import { Assignment, User, Course } from '../models/index.js';
import { authenticate, requireRole, hasPermission } from '../middleware/auth.js';
import { redisService } from '../services/redis.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';

const router = express.Router();

// Validation rules
const assignmentValidation = [
  body('title').trim().isLength({ min: 5 }).withMessage('Title must be at least 5 characters'),
  body('description').optional().trim(),
  body('course').isMongoId().withMessage('Valid course ID required'),
  body('dueDate').isISO8601().withMessage('Valid due date required'),
  body('maxScore').isFloat({ min: 1 }).withMessage('Max score must be at least 1'),
  body('type').isIn(['assignment', 'quiz', 'exam', 'project', 'participation']).withMessage('Invalid assignment type'),
  body('instructions').optional().trim(),
];

// GET /api/assignments - List assignments with filtering
router.get('/', authenticate, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      course, 
      lecturer, 
      type,
      status = 'all',
      sortBy = 'dueDate', 
      sortOrder = 'asc' 
    } = req.query;
    
    // Build filter query based on user role
    const filter = {};
    
    if (req.user.roles.includes('student')) {
      // Students see assignments for their enrolled courses
      const Enrollment = (await import('../models/index.js')).Enrollment;
      const enrollments = await Enrollment.find({ 
        student: req.user._id, 
        status: 'enrolled' 
      }).select('course');
      
      const courseIds = enrollments.map(e => e.course);
      filter.course = { $in: courseIds };
    } else if (req.user.roles.includes('lecturer')) {
      // Lecturers see assignments for their courses
      const lecturerCourses = await Course.find({ lecturer: req.user._id }).select('_id');
      filter.course = { $in: lecturerCourses.map(c => c._id) };
    }
    
    // Apply additional filters
    if (course) filter.course = course;
    if (lecturer && req.user.roles.includes('admin')) {
      const lecturerCourses = await Course.find({ lecturer }).select('_id');
      filter.course = { $in: lecturerCourses.map(c => c._id) };
    }
    if (type) filter.type = type;
    
    // Filter by status
    const now = new Date();
    if (status === 'upcoming') {
      filter.dueDate = { $gt: now };
    } else if (status === 'overdue') {
      filter.dueDate = { $lt: now };
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Execute query with pagination and population
    const assignments = await Assignment.find(filter)
      .populate('course', 'code title')
      .populate('lecturer', 'name email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Assignment.countDocuments(filter);

    res.json({
      success: true,
      data: {
        assignments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assignments',
      code: 'FETCH_ASSIGNMENTS_ERROR'
    });
  }
});

// GET /api/assignments/:id - Get assignment by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    
    const assignment = await Assignment.findById(id)
      .populate('course', 'code title')
      .populate('lecturer', 'name email')
      .populate('submissions.student', 'name email studentId');
    
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found',
        code: 'ASSIGNMENT_NOT_FOUND'
      });
    }

    // Check permissions
    if (req.user.roles.includes('student')) {
      // Check if student is enrolled in the course
      const Enrollment = (await import('../models/index.js')).Enrollment;
      const enrollment = await Enrollment.findOne({
        student: req.user._id,
        course: assignment.course._id,
        status: 'enrolled'
      });
      
      if (!enrollment) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          code: 'ACCESS_DENIED'
        });
      }
    }

    res.json({
      success: true,
      data: { assignment }
    });
  } catch (error) {
    logger.error('Error fetching assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assignment',
      code: 'FETCH_ASSIGNMENT_ERROR'
    });
  }
});

// POST /api/assignments - Create new assignment (Lecturer/Admin only)
router.post('/', authenticate, requireRole('lecturer', 'admin'), assignmentValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
        code: 'VALIDATION_ERROR'
      });
    }

    const { 
      title, 
      description, 
      course, 
      dueDate, 
      maxScore, 
      type, 
      instructions 
    } = req.body;

    // Verify course exists
    const courseObj = await Course.findById(course);
    if (!courseObj) {
      return res.status(400).json({
        success: false,
        message: 'Invalid course',
        code: 'INVALID_COURSE'
      });
    }

    // Check if lecturer can create assignment for this course
    if (req.user.roles.includes('lecturer') && courseObj.lecturer.toString() !== req.user._id) {
      return res.status(403).json({
        success: false,
        message: 'You can only create assignments for your own courses',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }

    // Create new assignment
    const assignment = new Assignment({
      title,
      description,
      course,
      lecturer: req.user._id,
      dueDate: new Date(dueDate),
      maxScore,
      type,
      instructions,
      isPublished: true,
      submissions: []
    });

    await assignment.save();

    // Populate assignment for response
    await assignment.populate([
      { path: 'course', select: 'code title' },
      { path: 'lecturer', select: 'name email' }
    ]);

    // Publish real-time update
    await publishRealtimeUpdate('assignment_created', {
      assignment: {
        id: assignment._id,
        title: assignment.title,
        courseId: course,
        courseCode: courseObj.code,
        dueDate: assignment.dueDate,
        type: assignment.type
      },
      createdBy: req.user._id
    });

    // Log creation
    auditLogger('assignment_created', 'assignment', req.user._id, {
      assignmentId: assignment._id,
      title: assignment.title,
      courseId: course,
      dueDate: assignment.dueDate
    });

    res.status(201).json({
      success: true,
      message: 'Assignment created successfully',
      data: { assignment }
    });
  } catch (error) {
    logger.error('Error creating assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create assignment',
      code: 'CREATE_ASSIGNMENT_ERROR'
    });
  }
});

// PUT /api/assignments/:id - Update assignment
router.put('/:id', authenticate, requireRole('lecturer', 'admin'), assignmentValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
        code: 'VALIDATION_ERROR'
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const assignment = await Assignment.findById(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found',
        code: 'ASSIGNMENT_NOT_FOUND'
      });
    }

    // Check permissions
    if (req.user.roles.includes('lecturer') && assignment.lecturer.toString() !== req.user._id) {
      return res.status(403).json({
        success: false,
        message: 'You can only modify your own assignments',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }

    // Update assignment fields
    Object.assign(assignment, updateData);
    if (updateData.dueDate) {
      assignment.dueDate = new Date(updateData.dueDate);
    }
    assignment.updatedAt = new Date();

    await assignment.save();

    // Populate assignment for response
    await assignment.populate([
      { path: 'course', select: 'code title' },
      { path: 'lecturer', select: 'name email' }
    ]);

    // Publish real-time update
    await publishRealtimeUpdate('assignment_updated', {
      assignment: {
        id: assignment._id,
        title: assignment.title,
        courseId: assignment.course._id,
        courseCode: assignment.course.code,
        dueDate: assignment.dueDate
      },
      updatedBy: req.user._id
    });

    // Log update
    auditLogger('assignment_updated', 'assignment', req.user._id, {
      assignmentId: assignment._id,
      changes: updateData
    });

    res.json({
      success: true,
      message: 'Assignment updated successfully',
      data: { assignment }
    });
  } catch (error) {
    logger.error('Error updating assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update assignment',
      code: 'UPDATE_ASSIGNMENT_ERROR'
    });
  }
});

// DELETE /api/assignments/:id - Delete assignment
router.delete('/:id', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
  try {
    const { id } = req.params;

    const assignment = await Assignment.findById(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found',
        code: 'ASSIGNMENT_NOT_FOUND'
      });
    }

    // Check permissions
    if (req.user.roles.includes('lecturer') && assignment.lecturer.toString() !== req.user._id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own assignments',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }

    await Assignment.findByIdAndDelete(id);

    // Publish real-time update
    await publishRealtimeUpdate('assignment_deleted', {
      assignmentId: assignment._id,
      title: assignment.title,
      courseId: assignment.course,
      deletedBy: req.user._id
    });

    // Log deletion
    auditLogger('assignment_deleted', 'assignment', req.user._id, {
      assignmentId: assignment._id,
      title: assignment.title,
      courseId: assignment.course
    });

    res.json({
      success: true,
      message: 'Assignment deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete assignment',
      code: 'DELETE_ASSIGNMENT_ERROR'
    });
  }
});

// POST /api/assignments/:id/submit - Submit assignment (Student only)
router.post('/:id/submit', authenticate, requireRole('student'), async (req, res) => {
  try {
    const { id } = req.params;
    const { files, comments } = req.body;

    const assignment = await Assignment.findById(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found',
        code: 'ASSIGNMENT_NOT_FOUND'
      });
    }

    // Check if assignment is still open
    if (new Date() > assignment.dueDate) {
      return res.status(400).json({
        success: false,
        message: 'Assignment deadline has passed',
        code: 'DEADLINE_PASSED'
      });
    }

    // Check if student is enrolled in the course
    const Enrollment = (await import('../models/index.js')).Enrollment;
    const enrollment = await Enrollment.findOne({
      student: req.user._id,
      course: assignment.course,
      status: 'enrolled'
    });

    if (!enrollment) {
      return res.status(403).json({
        success: false,
        message: 'You are not enrolled in this course',
        code: 'NOT_ENROLLED'
      });
    }

    // Check if already submitted
    const existingSubmission = assignment.submissions.find(
      sub => sub.student.toString() === req.user._id
    );

    if (existingSubmission) {
      return res.status(409).json({
        success: false,
        message: 'Assignment already submitted',
        code: 'ALREADY_SUBMITTED'
      });
    }

    // Add submission
    assignment.submissions.push({
      student: req.user._id,
      submittedAt: new Date(),
      files: files || [],
      comments: comments || ''
    });

    await assignment.save();

    // Publish real-time update
    await publishRealtimeUpdate('assignment_submitted', {
      assignment: {
        id: assignment._id,
        title: assignment.title,
        courseId: assignment.course,
        studentId: req.user._id,
        studentName: req.user.name,
        submittedAt: new Date()
      },
      submittedBy: req.user._id
    });

    // Log submission
    auditLogger('assignment_submitted', 'assignment', req.user._id, {
      assignmentId: assignment._id,
      courseId: assignment.course,
      submittedAt: new Date()
    });

    res.status(201).json({
      success: true,
      message: 'Assignment submitted successfully',
      data: {
        submission: assignment.submissions[assignment.submissions.length - 1]
      }
    });
  } catch (error) {
    logger.error('Error submitting assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit assignment',
      code: 'SUBMIT_ASSIGNMENT_ERROR'
    });
  }
});

// PUT /api/assignments/:id/grade - Grade assignment (Lecturer/Admin only)
router.put('/:id/grade', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
  try {
    const { id } = req.params;
    const { studentId, grade, feedback } = req.body;

    const assignment = await Assignment.findById(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found',
        code: 'ASSIGNMENT_NOT_FOUND'
      });
    }

    // Check permissions
    if (req.user.roles.includes('lecturer') && assignment.lecturer.toString() !== req.user._id) {
      return res.status(403).json({
        success: false,
        message: 'You can only grade assignments for your own courses',
        code: 'INSUFFICIENT_PERMISSION'
      });
    }

    // Find submission
    const submission = assignment.submissions.find(
      sub => sub.student.toString() === studentId
    );

    if (!submission) {
      return res.status(404).json({
        success: false,
        message: 'Submission not found',
        code: 'SUBMISSION_NOT_FOUND'
      });
    }

    // Update submission grade
    submission.grade = grade;
    submission.feedback = feedback;
    submission.gradedAt = new Date();
    submission.gradedBy = req.user._id;

    await assignment.save();

    // Create grade record
    const Grade = (await import('../models/index.js')).Grade;
    const gradeRecord = new Grade({
      student: studentId,
      course: assignment.course,
      assignment: assignment._id,
      score: grade,
      maxScore: assignment.maxScore,
      percentage: (grade / assignment.maxScore) * 100,
      type: assignment.type,
      semester: assignment.course.semester,
      academicYear: assignment.course.academicYear,
      gradedBy: req.user._id,
      gradedAt: new Date(),
      comments: feedback
    });

    await gradeRecord.save();

    // Publish real-time update
    await publishRealtimeUpdate('assignment_graded', {
      assignment: {
        id: assignment._id,
        title: assignment.title,
        courseId: assignment.course,
        studentId: studentId,
        grade: grade,
        maxScore: assignment.maxScore
      },
      gradedBy: req.user._id
    });

    // Log grading
    auditLogger('assignment_graded', 'assignment', req.user._id, {
      assignmentId: assignment._id,
      studentId: studentId,
      grade: grade,
      maxScore: assignment.maxScore
    });

    res.json({
      success: true,
      message: 'Assignment graded successfully',
      data: {
        submission,
        grade: gradeRecord
      }
    });
  } catch (error) {
    logger.error('Error grading assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to grade assignment',
      code: 'GRADE_ASSIGNMENT_ERROR'
    });
  }
});

export default router;
