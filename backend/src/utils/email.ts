import nodemailer from 'nodemailer';
import config from '../config/index.js';
import { logger } from './logger.js';

// Create transporter
const transporter = nodemailer.createTransporter({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.user,
    pass: config.email.password,
  },
});

// Verify transporter configuration
transporter.verify((error, success) => {
  if (error) {
    logger.error('Email transporter verification failed:', error);
  } else {
    logger.info('Email transporter is ready to send messages');
  }
});

// Email templates
const templates = {
  welcome: (data) => ({
    subject: 'Welcome to University Portal',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Welcome to University Portal!</h2>
        <p>Hello ${data.name},</p>
        <p>Your account has been successfully created. You can now access the university portal using your credentials.</p>
        <p>If you have any questions, please contact our support team.</p>
        <p>Best regards,<br>University Portal Team</p>
      </div>
    `,
    text: `Welcome to University Portal!\n\nHello ${data.name},\n\nYour account has been successfully created. You can now access the university portal using your credentials.\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nUniversity Portal Team`
  }),
  
  passwordReset: (data) => ({
    subject: 'Password Reset Request',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Password Reset Request</h2>
        <p>Hello ${data.name},</p>
        <p>You have requested to reset your password. Click the link below to reset your password:</p>
        <p><a href="${data.resetLink}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request this password reset, please ignore this email.</p>
        <p>Best regards,<br>University Portal Team</p>
      </div>
    `,
    text: `Password Reset Request\n\nHello ${data.name},\n\nYou have requested to reset your password. Visit the following link to reset your password:\n\n${data.resetLink}\n\nThis link will expire in 1 hour.\n\nIf you did not request this password reset, please ignore this email.\n\nBest regards,\nUniversity Portal Team`
  }),
  
  gradeNotification: (data) => ({
    subject: 'New Grade Posted',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Grade Posted</h2>
        <p>Hello ${data.studentName},</p>
        <p>A new grade has been posted for your course: <strong>${data.courseCode}</strong></p>
        <p>Assignment: ${data.assignmentTitle}</p>
        <p>Grade: <strong>${data.grade}/${data.maxScore}</strong></p>
        <p>You can view more details in your student portal.</p>
        <p>Best regards,<br>University Portal Team</p>
      </div>
    `,
    text: `New Grade Posted\n\nHello ${data.studentName},\n\nA new grade has been posted for your course: ${data.courseCode}\n\nAssignment: ${data.assignmentTitle}\nGrade: ${data.grade}/${data.maxScore}\n\nYou can view more details in your student portal.\n\nBest regards,\nUniversity Portal Team`
  }),
  
  assignmentDue: (data) => ({
    subject: 'Assignment Due Soon',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Assignment Due Soon</h2>
        <p>Hello ${data.studentName},</p>
        <p>This is a reminder that your assignment is due soon:</p>
        <p><strong>Course:</strong> ${data.courseCode}</p>
        <p><strong>Assignment:</strong> ${data.assignmentTitle}</p>
        <p><strong>Due Date:</strong> ${data.dueDate}</p>
        <p>Please make sure to submit your assignment before the deadline.</p>
        <p>Best regards,<br>University Portal Team</p>
      </div>
    `,
    text: `Assignment Due Soon\n\nHello ${data.studentName},\n\nThis is a reminder that your assignment is due soon:\n\nCourse: ${data.courseCode}\nAssignment: ${data.assignmentTitle}\nDue Date: ${data.dueDate}\n\nPlease make sure to submit your assignment before the deadline.\n\nBest regards,\nUniversity Portal Team`
  })
};

// Send email function
export async function sendEmail({ to, subject, template, data, html, text }) {
  try {
    let emailContent;
    
    if (template && templates[template]) {
      emailContent = templates[template](data);
    } else {
      emailContent = { subject, html, text };
    }

    const mailOptions = {
      from: config.email.from,
      to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
    };

    const result = await transporter.sendMail(mailOptions);
    logger.info('Email sent successfully:', { to, subject, messageId: result.messageId });
    
    return result;
  } catch (error) {
    logger.error('Error sending email:', error);
    throw error;
  }
}

// Send bulk emails
export async function sendBulkEmails(emails) {
  const results = [];
  const errors = [];

  for (const email of emails) {
    try {
      const result = await sendEmail(email);
      results.push(result);
    } catch (error) {
      errors.push({ email, error: error.message });
    }
  }

  return { results, errors };
}

export default { sendEmail, sendBulkEmails };
