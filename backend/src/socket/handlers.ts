import { Server as SocketIOServer } from 'socket.io';
import jwt from 'jsonwebtoken';
import { createAdapter } from '@socket.io/redis-adapter';

import config from '../config/index.js';
import { User } from '../models/index.js';
import { redisService } from '../services/redis.js';
import { logger, securityLogger } from '../utils/logger.js';
import { 
  REALTIME_EVENTS, 
  ROLE_NAMESPACES, 
  getNamespaceForRoles,
  createFilteredMessage,
  checkUpdateRateLimit 
} from '../utils/realtime.js';

// Socket.io rate limiting
const socketRateLimits = new Map();

export function setupSocketHandlers(io) {
  // Setup Redis adapter for horizontal scaling
  const pubClient = redisService.getClient();
  const subClient = pubClient.duplicate();
  io.adapter(createAdapter(pubClient, subClient));

  // Authentication middleware for Socket.io
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience,
      });

      // Get user from database
      const user = await User.findById(decoded.userId).select('-password -refreshTokens');
      if (!user || !user.isActive) {
        return next(new Error('User not found or inactive'));
      }

      socket.user = user;
      socket.userId = user._id;
      socket.userRoles = user.roles;
      
      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication failed'));
    }
  });

  // Handle connections for each role namespace
  Object.values(ROLE_NAMESPACES).forEach(namespace => {
    const nsp = io.of(namespace);
    
    nsp.on('connection', (socket) => {
      const user = socket.user;
      const userRoles = socket.userRoles;
      
      logger.info(`User connected to ${namespace}:`, {
        userId: user._id,
        email: user.email,
        roles: userRoles,
        socketId: socket.id
      });

      // Join user to their personal room
      socket.join(`user_${user._id}`);
      
      // Join user to role-based rooms
      userRoles.forEach(role => {
        socket.join(`role_${role}`);
      });

      // Join user to department room if applicable
      if (user.department) {
        socket.join(`department_${user.department}`);
      }

      // Handle real-time event subscriptions
      socket.on('subscribe', (data) => {
        try {
          const { events, filters } = data;
          
          if (Array.isArray(events)) {
            events.forEach(event => {
              if (Object.values(REALTIME_EVENTS).includes(event)) {
                socket.join(`event_${event}`);
                logger.debug(`User subscribed to event: ${event}`, {
                  userId: user._id,
                  socketId: socket.id
                });
              }
            });
          }
        } catch (error) {
          logger.error('Error handling subscription:', error);
        }
      });

      // Handle real-time event unsubscriptions
      socket.on('unsubscribe', (data) => {
        try {
          const { events } = data;
          
          if (Array.isArray(events)) {
            events.forEach(event => {
              socket.leave(`event_${event}`);
              logger.debug(`User unsubscribed from event: ${event}`, {
                userId: user._id,
                socketId: socket.id
              });
            });
          }
        } catch (error) {
          logger.error('Error handling unsubscription:', error);
        }
      });

      // Handle custom events based on namespace
      if (namespace === ROLE_NAMESPACES.student) {
        setupStudentHandlers(socket, user);
      } else if (namespace === ROLE_NAMESPACES.lecturer) {
        setupLecturerHandlers(socket, user);
      } else if (namespace === ROLE_NAMESPACES.admin) {
        setupAdminHandlers(socket, user);
      }

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info(`User disconnected from ${namespace}:`, {
          userId: user._id,
          email: user.email,
          socketId: socket.id,
          reason
        });
      });

      // Handle errors
      socket.on('error', (error) => {
        logger.error('Socket error:', {
          userId: user._id,
          socketId: socket.id,
          error: error.message
        });
      });
    });
  });

  // Subscribe to Redis real-time updates
  subscribeToRealtimeUpdates(io);
}

// Setup student-specific handlers
function setupStudentHandlers(socket, user) {
  // Join course-specific rooms
  socket.on('join_course', async (data) => {
    try {
      const { courseId } = data;
      
      // Verify student is enrolled in the course
      const Enrollment = (await import('../models/index.js')).Enrollment;
      const enrollment = await Enrollment.findOne({
        student: user._id,
        course: courseId,
        status: 'enrolled'
      });

      if (enrollment) {
        socket.join(`course_${courseId}`);
        logger.debug(`Student joined course room: ${courseId}`, {
          userId: user._id,
          socketId: socket.id
        });
      }
    } catch (error) {
      logger.error('Error joining course room:', error);
    }
  });

  // Leave course room
  socket.on('leave_course', (data) => {
    const { courseId } = data;
    socket.leave(`course_${courseId}`);
    logger.debug(`Student left course room: ${courseId}`, {
      userId: user._id,
      socketId: socket.id
    });
  });

  // Request grade updates
  socket.on('request_grade_updates', () => {
    socket.join('grade_updates');
    logger.debug('Student requested grade updates', {
      userId: user._id,
      socketId: socket.id
    });
  });
}

// Setup lecturer-specific handlers
function setupLecturerHandlers(socket, user) {
  // Join lecturer's course rooms
  socket.on('join_my_courses', async () => {
    try {
      const Course = (await import('../models/index.js')).Course;
      const courses = await Course.find({ lecturer: user._id, isActive: true });
      
      courses.forEach(course => {
        socket.join(`course_${course._id}`);
        socket.join(`lecturer_${course._id}`);
      });

      logger.debug(`Lecturer joined ${courses.length} course rooms`, {
        userId: user._id,
        socketId: socket.id
      });
    } catch (error) {
      logger.error('Error joining lecturer course rooms:', error);
    }
  });

  // Start attendance session
  socket.on('start_attendance', async (data) => {
    try {
      const { sessionId, courseId } = data;
      
      // Verify lecturer owns this course
      const Course = (await import('../models/index.js')).Course;
      const course = await Course.findOne({ _id: courseId, lecturer: user._id });
      
      if (course) {
        socket.join(`attendance_${sessionId}`);
        
        // Broadcast to all students in the course
        socket.to(`course_${courseId}`).emit('attendance_started', {
          sessionId,
          courseId,
          lecturer: user.name
        });

        logger.info('Attendance session started', {
          sessionId,
          courseId,
          lecturerId: user._id,
          socketId: socket.id
        });
      }
    } catch (error) {
      logger.error('Error starting attendance session:', error);
    }
  });

  // End attendance session
  socket.on('end_attendance', (data) => {
    const { sessionId, courseId } = data;
    
    socket.leave(`attendance_${sessionId}`);
    
    // Broadcast to all students in the course
    socket.to(`course_${courseId}`).emit('attendance_ended', {
      sessionId,
      courseId
    });

    logger.info('Attendance session ended', {
      sessionId,
      courseId,
      lecturerId: user._id,
      socketId: socket.id
    });
  });
}

// Setup admin-specific handlers
function setupAdminHandlers(socket, user) {
  // Join system-wide rooms
  socket.on('join_system_monitoring', () => {
    socket.join('system_monitoring');
    socket.join('admin_alerts');
    logger.debug('Admin joined system monitoring', {
      userId: user._id,
      socketId: socket.id
    });
  });

  // Broadcast system announcements
  socket.on('broadcast_announcement', (data) => {
    try {
      const { message, targetRoles, targetDepartments } = data;
      
      const announcement = {
        id: `announcement_${Date.now()}`,
        message,
        from: user.name,
        timestamp: new Date().toISOString(),
        type: 'announcement'
      };

      // Broadcast to specific roles
      if (targetRoles && Array.isArray(targetRoles)) {
        targetRoles.forEach(role => {
          socket.to(`role_${role}`).emit('system_announcement', announcement);
        });
      }

      // Broadcast to specific departments
      if (targetDepartments && Array.isArray(targetDepartments)) {
        targetDepartments.forEach(dept => {
          socket.to(`department_${dept}`).emit('system_announcement', announcement);
        });
      }

      logger.info('System announcement broadcasted', {
        announcementId: announcement.id,
        adminId: user._id,
        targetRoles,
        targetDepartments
      });
    } catch (error) {
      logger.error('Error broadcasting announcement:', error);
    }
  });
}

// Subscribe to Redis real-time updates and broadcast to connected clients
function subscribeToRealtimeUpdates(io) {
  redisService.subscribe('realtime_updates', (message) => {
    try {
      const { type, data, timestamp, id } = message;
      
      logger.debug('Received real-time update from Redis:', { type, id });

      // Broadcast to all namespaces
      Object.values(ROLE_NAMESPACES).forEach(namespace => {
        const nsp = io.of(namespace);
        
        nsp.emit('realtime_update', {
          type,
          data,
          timestamp,
          id
        });
      });

      // Send targeted updates based on event type
      handleTargetedUpdates(io, type, data);
      
    } catch (error) {
      logger.error('Error processing real-time update:', error);
    }
  });
}

// Handle targeted updates for specific events
function handleTargetedUpdates(io, eventType, data) {
  switch (eventType) {
    case REALTIME_EVENTS.GRADE_CREATED:
    case REALTIME_EVENTS.GRADE_UPDATED:
      // Send to specific student
      if (data.studentId) {
        io.of(ROLE_NAMESPACES.student).to(`user_${data.studentId}`).emit('grade_update', {
          type: eventType,
          data,
          timestamp: new Date().toISOString()
        });
      }
      break;

    case REALTIME_EVENTS.ASSIGNMENT_SUBMITTED:
      // Send to course lecturer
      if (data.courseId) {
        io.of(ROLE_NAMESPACES.lecturer).to(`course_${data.courseId}`).emit('assignment_submitted', {
          type: eventType,
          data,
          timestamp: new Date().toISOString()
        });
      }
      break;

    case REALTIME_EVENTS.ATTENDANCE_MARKED:
      // Send to student and lecturer
      if (data.studentId) {
        io.of(ROLE_NAMESPACES.student).to(`user_${data.studentId}`).emit('attendance_update', {
          type: eventType,
          data,
          timestamp: new Date().toISOString()
        });
      }
      if (data.courseId) {
        io.of(ROLE_NAMESPACES.lecturer).to(`course_${data.courseId}`).emit('attendance_update', {
          type: eventType,
          data,
          timestamp: new Date().toISOString()
        });
      }
      break;

    case REALTIME_EVENTS.NOTIFICATION_CREATED:
      // Send to specific user
      if (data.userId) {
        Object.values(ROLE_NAMESPACES).forEach(namespace => {
          io.of(namespace).to(`user_${data.userId}`).emit('notification', {
            type: eventType,
            data,
            timestamp: new Date().toISOString()
          });
        });
      }
      break;
  }
}

// Rate limiting for socket events
function checkSocketRateLimit(socket, eventType) {
  const userId = socket.userId;
  const key = `socket_${userId}_${eventType}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxEvents = 20; // Max 20 events per minute per type

  if (!socketRateLimits.has(key)) {
    socketRateLimits.set(key, { count: 1, windowStart: now });
    return true;
  }

  const counts = socketRateLimits.get(key);
  
  if (now - counts.windowStart > windowMs) {
    counts.count = 1;
    counts.windowStart = now;
    return true;
  }

  if (counts.count >= maxEvents) {
    return false;
  }

  counts.count++;
  return true;
}

// Clean up rate limit entries
setInterval(() => {
  const now = Date.now();
  const windowMs = 60000;
  
  for (const [key, counts] of socketRateLimits.entries()) {
    if (now - counts.windowStart > windowMs) {
      socketRateLimits.delete(key);
    }
  }
}, 300000); // Clean up every 5 minutes
