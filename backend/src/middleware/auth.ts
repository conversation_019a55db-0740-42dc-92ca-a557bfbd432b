import jwt from 'jsonwebtoken';
import { promisify } from 'util';
import config from '../config/index.js';
import { User, Permission } from '../models/index.js';
import { redisClient } from '../services/redis.js';
import { logger } from '../utils/logger.js';

const verifyToken = promisify(jwt.verify);

// JWT Token Management
export class TokenManager {
  static generateAccessToken(payload) {
    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.accessExpiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
    });
  }

  static generateRefreshToken(payload) {
    return jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
    });
  }

  static async verifyAccessToken(token) {
    try {
      return await verifyToken(token, config.jwt.secret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience,
      });
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  static async verifyRefreshToken(token) {
    try {
      return await verifyToken(token, config.jwt.refreshSecret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience,
      });
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  static async blacklistToken(jti) {
    const ttl = 15 * 60; // 15 minutes
    await redisClient.setex(`blacklist:${jti}`, ttl, 'true');
  }

  static async isTokenBlacklisted(jti) {
    const result = await redisClient.get(`blacklist:${jti}`);
    return result === 'true';
  }
}

// Authentication Middleware
export const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
        code: 'MISSING_TOKEN'
      });
    }

    const token = authHeader.substring(7);
    
    // Check if token is blacklisted
    const decoded = await TokenManager.verifyAccessToken(token);
    const isBlacklisted = await TokenManager.isTokenBlacklisted(decoded.jti);
    
    if (isBlacklisted) {
      return res.status(401).json({
        success: false,
        message: 'Token has been revoked',
        code: 'TOKEN_REVOKED'
      });
    }

    // Get user from cache or database
    let user = await getUserFromCache(decoded.userId);
    if (!user) {
      user = await User.findById(decoded.userId).select('-password -refreshTokens');
      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'User not found or inactive',
          code: 'USER_NOT_FOUND'
        });
      }
      await cacheUser(user);
    }

    req.user = user;
    req.token = token;
    req.tokenPayload = decoded;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      code: 'INVALID_TOKEN'
    });
  }
};

// Role-based Access Control Middleware
export const requireRole = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRoles = req.user.roles;
    const hasRole = allowedRoles.some(role => userRoles.includes(role));

    if (!hasRole) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        code: 'INSUFFICIENT_ROLE'
      });
    }

    next();
  };
};

// Permission-based Access Control Middleware
export const hasPermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      // Check cache first
      const cacheKey = `permissions:${req.user._id}`;
      let userPermissions = await redisClient.get(cacheKey);
      
      if (userPermissions) {
        userPermissions = JSON.parse(userPermissions);
      } else {
        // Get permissions from database
        userPermissions = await getUserPermissions(req.user._id, req.user.roles);
        await redisClient.setex(cacheKey, config.cache.ttl.permissions, JSON.stringify(userPermissions));
      }

      if (!userPermissions.includes(permission)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSION'
        });
      }

      next();
    } catch (error) {
      logger.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Permission check failed',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

// MFA Verification Middleware
export const requireMFA = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Check if MFA is required for this user's roles
    const mfaRequiredRoles = ['lecturer', 'admin', 'finance', 'registrar'];
    const requiresMFA = req.user.roles.some(role => mfaRequiredRoles.includes(role));

    if (requiresMFA && !req.user.mfaEnabled) {
      return res.status(403).json({
        success: false,
        message: 'MFA setup required',
        code: 'MFA_SETUP_REQUIRED'
      });
    }

    // Check MFA verification in session/token
    if (requiresMFA && req.user.mfaEnabled) {
      const mfaVerified = req.tokenPayload?.mfaVerified;
      if (!mfaVerified) {
        return res.status(403).json({
          success: false,
          message: 'MFA verification required',
          code: 'MFA_VERIFICATION_REQUIRED'
        });
      }
    }

    next();
  } catch (error) {
    logger.error('MFA check error:', error);
    return res.status(500).json({
      success: false,
      message: 'MFA check failed',
      code: 'MFA_CHECK_ERROR'
    });
  }
};

// Resource Ownership Middleware
export const requireOwnership = (resourceField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Admin users can access any resource
    if (req.user.roles.includes('admin')) {
      return next();
    }

    const resourceUserId = req.params[resourceField] || req.body[resourceField];
    
    if (resourceUserId && resourceUserId !== req.user._id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this resource',
        code: 'RESOURCE_ACCESS_DENIED'
      });
    }

    next();
  };
};

// Helper Functions
async function getUserFromCache(userId) {
  try {
    const cacheKey = `user:${userId}`;
    const cachedUser = await redisClient.get(cacheKey);
    return cachedUser ? JSON.parse(cachedUser) : null;
  } catch (error) {
    logger.error('Error getting user from cache:', error);
    return null;
  }
}

async function cacheUser(user) {
  try {
    const cacheKey = `user:${user._id}`;
    await redisClient.setex(cacheKey, config.cache.ttl.users, JSON.stringify(user));
  } catch (error) {
    logger.error('Error caching user:', error);
  }
}

async function getUserPermissions(userId, userRoles) {
  try {
    const permissions = await Permission.find({
      $or: [
        { roles: { $in: userRoles } },
        { isActive: true }
      ]
    }).select('name');

    return permissions.map(p => p.name);
  } catch (error) {
    logger.error('Error getting user permissions:', error);
    return [];
  }
}

// Rate Limiting Middleware
export const createRateLimit = (windowMs, maxRequests, keyGenerator) => {
  return async (req, res, next) => {
    try {
      const key = keyGenerator(req);
      const current = await redisClient.incr(key);
      
      if (current === 1) {
        await redisClient.expire(key, Math.ceil(windowMs / 1000));
      }
      
      if (current > maxRequests) {
        return res.status(429).json({
          success: false,
          message: 'Too many requests',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }
      
      res.set('X-RateLimit-Limit', maxRequests);
      res.set('X-RateLimit-Remaining', Math.max(0, maxRequests - current));
      
      next();
    } catch (error) {
      logger.error('Rate limiting error:', error);
      next(); // Continue on error to avoid blocking requests
    }
  };
};

// Login Rate Limiting
export const loginRateLimit = createRateLimit(
  config.rateLimit.windowMs,
  5, // 5 login attempts per window
  (req) => `login:${req.ip}:${req.body.email}`
);

// API Rate Limiting
export const apiRateLimit = createRateLimit(
  config.rateLimit.windowMs,
  config.rateLimit.maxRequests,
  (req) => `api:${req.ip}`
);

// Socket.io Rate Limiting
export const socketRateLimit = createRateLimit(
  config.socket.rateLimit.window,
  config.socket.rateLimit.max,
  (req) => `socket:${req.socket.id}`
);
