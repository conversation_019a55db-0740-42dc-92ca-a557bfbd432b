{"name": "tsc-watch", "version": "7.2.0", "description": "The TypeScript compiler with onSuccess command", "scripts": {"clean": "rimraf dist && rimraf tmp", "prepublishOnly": "npm run build && crlf --set=LF index.js client.js dist/**/*", "test": "npm run build && jest --verbose --runInBand", "build": "npm run clean && npm run build-lib && npm run build-client", "build-lib": "tsc -p tsconfig.json", "build-client": "tsc -p tsconfig-client.json", "watch-lib": "tsc -p tsconfig.json --watch"}, "bin": {"tsc-watch": "dist/lib/tsc-watch.js"}, "files": ["dist/**/*", "index.js", "client.js", "client.d.ts"], "repository": {"type": "git", "url": "git+ssh://**************/gilamran/tsc-watch.git"}, "keywords": ["TypeScript"], "main": "./dist/client/index.js", "types": "./dist/client/index.d.ts", "engines": {"node": ">=12.12.0"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/gilamran/tsc-watch/issues"}, "homepage": "https://github.com/gilamran/tsc-watch#readme", "prettier": {"trailingComma": "all", "tabWidth": 2, "semi": true, "singleQuote": true, "printWidth": 100}, "dependencies": {"cross-spawn": "^7.0.6", "node-cleanup": "^2.1.2", "ps-tree": "^1.2.0", "string-argv": "^0.3.2"}, "peerDependencies": {"typescript": "*"}, "devDependencies": {"@types/cross-spawn": "^6.0.6", "@types/jest": "^29.5.14", "@types/node": "^12.12.0", "@types/node-cleanup": "^2.1.5", "@types/ps-tree": "^1.1.6", "crlf": "^1.1.1", "fs-extra": "^11.3.0", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}}